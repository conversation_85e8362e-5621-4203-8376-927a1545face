"""
创建业务相关的自定义意图类型
根据实际业务需求创建常用的意图类型
"""
import asyncio
import json
import uuid
from pathlib import Path
from datetime import datetime

# 复用简化的类定义
from simple_intent_test import CustomIntentType, IntentPattern, IntentExample, SimpleJsonStorage, SimpleIntentRecognizer


async def create_business_intents():
    """创建业务相关的意图类型"""
    print("🏢 创建业务相关的自定义意图类型")
    print("=" * 50)
    
    # 初始化存储
    storage = SimpleJsonStorage()
    await storage.initialize()
    
    # 1. 账户相关意图
    print("\n👤 创建账户相关意图...")
    account_intent = CustomIntentType(
        intent_id=str(uuid.uuid4()),
        name="account_management",
        display_name="账户管理",
        description="用户询问账户相关问题",
        category="account",
        priority=95
    )
    await storage.save_intent(account_intent)
    
    # 账户相关模式
    account_patterns = [
        IntentPattern(str(uuid.uuid4()), account_intent.intent_id, "keyword", "账户", 1.5),
        IntentPattern(str(uuid.uuid4()), account_intent.intent_id, "keyword", "账号", 1.5),
        IntentPattern(str(uuid.uuid4()), account_intent.intent_id, "keyword", "登录", 1.2),
        IntentPattern(str(uuid.uuid4()), account_intent.intent_id, "keyword", "注册", 1.2),
        IntentPattern(str(uuid.uuid4()), account_intent.intent_id, "keyword", "密码", 1.3),
        IntentPattern(str(uuid.uuid4()), account_intent.intent_id, "regex", r"(忘记密码|找回密码|重置密码)", 1.8),
        IntentPattern(str(uuid.uuid4()), account_intent.intent_id, "regex", r"(无法登录|登录不了|登录失败)", 1.6)
    ]
    
    for pattern in account_patterns:
        await storage.save_pattern(pattern)
    
    # 账户相关示例
    account_examples = [
        IntentExample(str(uuid.uuid4()), account_intent.intent_id, "我忘记了账户密码", 0.9, True),
        IntentExample(str(uuid.uuid4()), account_intent.intent_id, "如何注册新账号？", 0.8, True),
        IntentExample(str(uuid.uuid4()), account_intent.intent_id, "登录不了怎么办？", 0.85, True),
        IntentExample(str(uuid.uuid4()), account_intent.intent_id, "今天天气很好", 0.1, False)
    ]
    
    for example in account_examples:
        await storage.save_example(example)
    
    # 2. 订单相关意图
    print("\n📦 创建订单相关意图...")
    order_intent = CustomIntentType(
        intent_id=str(uuid.uuid4()),
        name="order_inquiry",
        display_name="订单查询",
        description="用户询问订单相关问题",
        category="order",
        priority=90
    )
    await storage.save_intent(order_intent)
    
    # 订单相关模式
    order_patterns = [
        IntentPattern(str(uuid.uuid4()), order_intent.intent_id, "keyword", "订单", 2.0),
        IntentPattern(str(uuid.uuid4()), order_intent.intent_id, "keyword", "物流", 1.5),
        IntentPattern(str(uuid.uuid4()), order_intent.intent_id, "keyword", "快递", 1.5),
        IntentPattern(str(uuid.uuid4()), order_intent.intent_id, "keyword", "发货", 1.3),
        IntentPattern(str(uuid.uuid4()), order_intent.intent_id, "keyword", "配送", 1.3),
        IntentPattern(str(uuid.uuid4()), order_intent.intent_id, "regex", r"(订单状态|订单进度|订单查询)", 1.8),
        IntentPattern(str(uuid.uuid4()), order_intent.intent_id, "regex", r"(什么时候到|何时送达|多久能到)", 1.6)
    ]
    
    for pattern in order_patterns:
        await storage.save_pattern(pattern)
    
    # 订单相关示例
    order_examples = [
        IntentExample(str(uuid.uuid4()), order_intent.intent_id, "我的订单什么时候发货？", 0.9, True),
        IntentExample(str(uuid.uuid4()), order_intent.intent_id, "查询订单状态", 0.95, True),
        IntentExample(str(uuid.uuid4()), order_intent.intent_id, "快递什么时候到？", 0.8, True),
        IntentExample(str(uuid.uuid4()), order_intent.intent_id, "我想买个苹果", 0.1, False)
    ]
    
    for example in order_examples:
        await storage.save_example(example)
    
    # 3. 退换货意图
    print("\n🔄 创建退换货意图...")
    return_intent = CustomIntentType(
        intent_id=str(uuid.uuid4()),
        name="return_refund",
        display_name="退换货",
        description="用户询问退换货相关问题",
        category="service",
        priority=85
    )
    await storage.save_intent(return_intent)
    
    # 退换货模式
    return_patterns = [
        IntentPattern(str(uuid.uuid4()), return_intent.intent_id, "keyword", "退货", 2.0),
        IntentPattern(str(uuid.uuid4()), return_intent.intent_id, "keyword", "换货", 2.0),
        IntentPattern(str(uuid.uuid4()), return_intent.intent_id, "keyword", "退款", 1.8),
        IntentPattern(str(uuid.uuid4()), return_intent.intent_id, "keyword", "退换", 1.8),
        IntentPattern(str(uuid.uuid4()), return_intent.intent_id, "keyword", "不满意", 1.5),
        IntentPattern(str(uuid.uuid4()), return_intent.intent_id, "regex", r"(申请退货|申请退款|要求退货)", 2.2),
        IntentPattern(str(uuid.uuid4()), return_intent.intent_id, "regex", r"(质量问题|有问题|坏了)", 1.6)
    ]
    
    for pattern in return_patterns:
        await storage.save_pattern(pattern)
    
    # 退换货示例
    return_examples = [
        IntentExample(str(uuid.uuid4()), return_intent.intent_id, "我要申请退货", 0.95, True),
        IntentExample(str(uuid.uuid4()), return_intent.intent_id, "商品有质量问题，可以退款吗？", 0.9, True),
        IntentExample(str(uuid.uuid4()), return_intent.intent_id, "不满意，想换货", 0.85, True),
        IntentExample(str(uuid.uuid4()), return_intent.intent_id, "今天心情不错", 0.1, False)
    ]
    
    for example in return_examples:
        await storage.save_example(example)
    
    # 4. 优惠活动意图
    print("\n🎁 创建优惠活动意图...")
    promotion_intent = CustomIntentType(
        intent_id=str(uuid.uuid4()),
        name="promotion_inquiry",
        display_name="优惠活动",
        description="用户询问优惠活动相关问题",
        category="marketing",
        priority=80
    )
    await storage.save_intent(promotion_intent)
    
    # 优惠活动模式
    promotion_patterns = [
        IntentPattern(str(uuid.uuid4()), promotion_intent.intent_id, "keyword", "优惠", 1.8),
        IntentPattern(str(uuid.uuid4()), promotion_intent.intent_id, "keyword", "折扣", 1.8),
        IntentPattern(str(uuid.uuid4()), promotion_intent.intent_id, "keyword", "活动", 1.5),
        IntentPattern(str(uuid.uuid4()), promotion_intent.intent_id, "keyword", "促销", 1.6),
        IntentPattern(str(uuid.uuid4()), promotion_intent.intent_id, "keyword", "券", 1.4),
        IntentPattern(str(uuid.uuid4()), promotion_intent.intent_id, "regex", r"(优惠券|代金券|折扣券)", 2.0),
        IntentPattern(str(uuid.uuid4()), promotion_intent.intent_id, "regex", r"(有什么活动|最新活动|当前优惠)", 1.8)
    ]
    
    for pattern in promotion_patterns:
        await storage.save_pattern(pattern)
    
    # 优惠活动示例
    promotion_examples = [
        IntentExample(str(uuid.uuid4()), promotion_intent.intent_id, "现在有什么优惠活动？", 0.9, True),
        IntentExample(str(uuid.uuid4()), promotion_intent.intent_id, "有优惠券吗？", 0.85, True),
        IntentExample(str(uuid.uuid4()), promotion_intent.intent_id, "最新的折扣信息", 0.8, True),
        IntentExample(str(uuid.uuid4()), promotion_intent.intent_id, "我想学习编程", 0.1, False)
    ]
    
    for example in promotion_examples:
        await storage.save_example(example)
    
    print("\n✅ 业务意图创建完成！")
    return storage


async def test_business_intents():
    """测试业务意图识别"""
    print("\n🧪 测试业务意图识别...")
    
    storage = SimpleJsonStorage()
    recognizer = SimpleIntentRecognizer(storage)
    
    # 测试查询
    business_test_queries = [
        "我忘记了登录密码",
        "订单什么时候能到？",
        "这个商品质量有问题，我要退货",
        "现在有什么优惠活动吗？",
        "账户被锁定了怎么办？",
        "快递还没到，查一下物流",
        "不满意，申请退款",
        "有没有折扣券？",
        "今天天气真好",  # 负例
        "我想学习Python"  # 负例
    ]
    
    for query in business_test_queries:
        print(f"\n🔍 测试查询: '{query}'")
        results = await recognizer.recognize_intent(query)
        
        if results:
            best_result = results[0]
            print(f"  ✅ 最佳匹配: {best_result['display_name']}")
            print(f"  📊 置信度: {best_result['confidence']:.3f}")
            print(f"  🏷️ 分类: {await get_intent_category(storage, best_result['intent_id'])}")
            
            if len(results) > 1:
                print("  📋 其他可能匹配:")
                for result in results[1:3]:  # 显示前3个结果
                    if result['confidence'] > 0.2:  # 只显示置信度较高的
                        print(f"    - {result['display_name']}: {result['confidence']:.3f}")
        else:
            print("  ❌ 未识别到匹配的意图")


async def get_intent_category(storage, intent_id):
    """获取意图分类"""
    intents = await storage.load_intents()
    for intent in intents:
        if intent["intent_id"] == intent_id:
            return intent.get("category", "unknown")
    return "unknown"


async def show_statistics():
    """显示统计信息"""
    print("\n📊 业务意图统计信息...")
    
    storage = SimpleJsonStorage()
    intents = await storage.load_intents()
    
    # 按分类统计
    categories = {}
    for intent in intents:
        category = intent.get("category", "unknown")
        if category not in categories:
            categories[category] = []
        categories[category].append(intent)
    
    print(f"📈 总计 {len(intents)} 个自定义意图类型")
    print("\n📋 分类统计:")
    for category, intent_list in categories.items():
        print(f"  🏷️ {category}: {len(intent_list)} 个意图")
        for intent in intent_list:
            patterns = await storage.load_patterns(intent["intent_id"])
            print(f"    - {intent['display_name']} (优先级: {intent['priority']}, 模式: {len(patterns)})")


async def main():
    """主函数"""
    # 创建业务意图
    await create_business_intents()
    
    # 测试意图识别
    await test_business_intents()
    
    # 显示统计信息
    await show_statistics()
    
    print("\n🎉 业务意图创建和测试完成！")
    print("📁 数据已保存到: data/custom_intents/")


if __name__ == "__main__":
    asyncio.run(main())
