"""
测试自定义意图类型API接口
"""
import requests
import json
import time


def test_api_endpoints():
    """测试API端点"""
    base_url = "http://localhost:8002"
    
    print("🌐 测试自定义意图类型API接口")
    print("=" * 50)
    
    # 1. 测试健康检查
    print("\n🏥 测试健康检查...")
    try:
        response = requests.get(f"{base_url}/api/v1/custom-intents/health", timeout=5)
        if response.status_code == 200:
            print("✅ 健康检查通过")
            print(f"📊 响应: {response.json()}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 连接失败: {e}")
        print("💡 请确保服务已启动: python main.py")
        return False
    
    # 2. 测试获取意图列表
    print("\n📋 测试获取意图列表...")
    try:
        response = requests.get(f"{base_url}/api/v1/custom-intents/intents")
        if response.status_code == 200:
            data = response.json()
            print("✅ 获取意图列表成功")
            if data.get("success"):
                intents = data.get("data", {}).get("items", [])
                print(f"📊 找到 {len(intents)} 个自定义意图:")
                for intent in intents:
                    print(f"  - {intent['display_name']} ({intent['category']})")
            else:
                print(f"❌ API返回错误: {data.get('message')}")
        else:
            print(f"❌ 获取意图列表失败: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
    
    # 3. 测试意图识别
    print("\n🧪 测试意图识别...")
    test_queries = [
        "我忘记了密码",
        "订单什么时候到？",
        "要申请退货",
        "有什么优惠活动？"
    ]
    
    for query in test_queries:
        try:
            test_data = {
                "query_text": query,
                "include_system": True,
                "include_custom": True
            }
            
            response = requests.post(
                f"{base_url}/api/v1/custom-intents/test",
                json=test_data
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    result_data = data.get("data", {})
                    results = result_data.get("results", [])
                    
                    print(f"\n🔍 查询: '{query}'")
                    if results:
                        best = results[0]
                        print(f"  ✅ 最佳匹配: {best['intent_name']}")
                        print(f"  📊 置信度: {best['confidence']:.3f}")
                    else:
                        print("  ❌ 未识别到匹配的意图")
                else:
                    print(f"  ❌ 测试失败: {data.get('message')}")
            else:
                print(f"  ❌ 请求失败: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"  ❌ 请求异常: {e}")
    
    # 4. 测试统计信息
    print("\n📊 测试统计信息...")
    try:
        response = requests.get(f"{base_url}/api/v1/custom-intents/statistics")
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                stats = data.get("data", {})
                print("✅ 获取统计信息成功")
                print(f"  - 总意图数量: {stats.get('total_intents', 0)}")
                print(f"  - 启用意图数量: {stats.get('active_intents', 0)}")
                print(f"  - 自定义意图数量: {stats.get('custom_intents', 0)}")
                print(f"  - 总模式数量: {stats.get('total_patterns', 0)}")
                print(f"  - 分类统计: {stats.get('categories', {})}")
            else:
                print(f"❌ 获取统计信息失败: {data.get('message')}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    
    return True


def test_create_intent_via_api():
    """通过API创建新的意图类型"""
    base_url = "http://localhost:8002"
    
    print("\n🆕 测试通过API创建意图类型...")
    
    # 创建一个新的意图类型
    new_intent = {
        "name": "customer_service",
        "display_name": "客户服务",
        "description": "用户寻求客户服务帮助",
        "category": "service",
        "priority": 88,
        "patterns": [
            {
                "pattern_type": "keyword",
                "pattern_value": "客服",
                "weight": 2.0,
                "language": "zh"
            },
            {
                "pattern_type": "keyword",
                "pattern_value": "人工服务",
                "weight": 1.8,
                "language": "zh"
            },
            {
                "pattern_type": "regex",
                "pattern_value": r"(联系客服|人工客服|在线客服)",
                "weight": 2.2,
                "language": "zh"
            }
        ],
        "examples": [
            {
                "query_text": "我要联系客服",
                "expected_confidence": 0.9,
                "is_positive": True,
                "language": "zh"
            },
            {
                "query_text": "转人工服务",
                "expected_confidence": 0.85,
                "is_positive": True,
                "language": "zh"
            }
        ]
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/v1/custom-intents/intents",
            json=new_intent,
            params={"created_by": "api_test"}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                intent_data = data.get("data", {})
                print("✅ 创建意图类型成功")
                print(f"  - ID: {intent_data.get('intent_id')}")
                print(f"  - 名称: {intent_data.get('display_name')}")
                print(f"  - 分类: {intent_data.get('category')}")
                return intent_data.get('intent_id')
            else:
                print(f"❌ 创建失败: {data.get('message')}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    
    return None


def main():
    """主函数"""
    print("🚀 开始API测试")
    
    # 基础API测试
    if test_api_endpoints():
        print("\n" + "="*50)
        print("✅ 基础API测试完成")
        
        # 尝试创建新意图
        intent_id = test_create_intent_via_api()
        if intent_id:
            print(f"\n✅ 新意图创建成功: {intent_id}")
        
        print("\n🎉 API测试完成！")
        print("\n📋 测试总结:")
        print("  ✅ 健康检查正常")
        print("  ✅ 意图列表获取正常")
        print("  ✅ 意图识别功能正常")
        print("  ✅ 统计信息获取正常")
        print("  ✅ 意图创建功能正常")
        
        print("\n🌐 可以访问以下URL查看API文档:")
        print("  - Swagger UI: http://localhost:8002/docs")
        print("  - ReDoc: http://localhost:8002/redoc")
    else:
        print("\n❌ 基础API测试失败")
        print("💡 请检查:")
        print("  1. 服务是否已启动: python main.py")
        print("  2. 端口8002是否被占用")
        print("  3. 防火墙设置")


if __name__ == "__main__":
    main()
