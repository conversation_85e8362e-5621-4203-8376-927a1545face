"""
简化的自定义意图类型测试脚本
不依赖项目其他模块，独立测试核心功能
"""
import asyncio
import json
import uuid
import re
from pathlib import Path
from datetime import datetime
from typing import List, Optional, Dict, Any, Tuple


# 简化的数据模型
class CustomIntentType:
    def __init__(self, intent_id: str, name: str, display_name: str, 
                 description: str = None, category: str = "custom", 
                 priority: int = 100, is_active: bool = True):
        self.intent_id = intent_id
        self.name = name
        self.display_name = display_name
        self.description = description
        self.category = category
        self.priority = priority
        self.is_active = is_active
        self.created_at = datetime.now().isoformat()
    
    def dict(self):
        return {
            "intent_id": self.intent_id,
            "name": self.name,
            "display_name": self.display_name,
            "description": self.description,
            "category": self.category,
            "priority": self.priority,
            "is_active": self.is_active,
            "created_at": self.created_at
        }


class IntentPattern:
    def __init__(self, pattern_id: str, intent_id: str, pattern_type: str,
                 pattern_value: str, weight: float = 1.0, is_active: bool = True):
        self.pattern_id = pattern_id
        self.intent_id = intent_id
        self.pattern_type = pattern_type
        self.pattern_value = pattern_value
        self.weight = weight
        self.is_active = is_active
        self.created_at = datetime.now().isoformat()
    
    def dict(self):
        return {
            "pattern_id": self.pattern_id,
            "intent_id": self.intent_id,
            "pattern_type": self.pattern_type,
            "pattern_value": self.pattern_value,
            "weight": self.weight,
            "is_active": self.is_active,
            "created_at": self.created_at
        }


class IntentExample:
    def __init__(self, example_id: str, intent_id: str, query_text: str,
                 expected_confidence: float = 0.8, is_positive: bool = True):
        self.example_id = example_id
        self.intent_id = intent_id
        self.query_text = query_text
        self.expected_confidence = expected_confidence
        self.is_positive = is_positive
        self.created_at = datetime.now().isoformat()
    
    def dict(self):
        return {
            "example_id": self.example_id,
            "intent_id": self.intent_id,
            "query_text": self.query_text,
            "expected_confidence": self.expected_confidence,
            "is_positive": self.is_positive,
            "created_at": self.created_at
        }


# 简化的存储实现
class SimpleJsonStorage:
    def __init__(self, storage_dir: str = "./data/custom_intents"):
        self.storage_dir = Path(storage_dir)
        self.intents_file = self.storage_dir / "intents.json"
        self.patterns_file = self.storage_dir / "patterns.json"
        self.examples_file = self.storage_dir / "examples.json"
    
    async def initialize(self):
        """初始化存储"""
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        
        for file_path in [self.intents_file, self.patterns_file, self.examples_file]:
            if not file_path.exists():
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump([], f)
        
        print(f"✅ 存储初始化完成: {self.storage_dir}")
        return True
    
    async def save_intent(self, intent: CustomIntentType):
        """保存意图"""
        intents = await self._load_json(self.intents_file)
        intents.append(intent.dict())
        await self._save_json(self.intents_file, intents)
        print(f"✅ 保存意图: {intent.name}")
    
    async def save_pattern(self, pattern: IntentPattern):
        """保存模式"""
        patterns = await self._load_json(self.patterns_file)
        patterns.append(pattern.dict())
        await self._save_json(self.patterns_file, patterns)
        print(f"✅ 保存模式: {pattern.pattern_value}")
    
    async def save_example(self, example: IntentExample):
        """保存示例"""
        examples = await self._load_json(self.examples_file)
        examples.append(example.dict())
        await self._save_json(self.examples_file, examples)
        print(f"✅ 保存示例: {example.query_text}")
    
    async def load_intents(self) -> List[Dict]:
        """加载所有意图"""
        return await self._load_json(self.intents_file)
    
    async def load_patterns(self, intent_id: str) -> List[Dict]:
        """加载指定意图的模式"""
        patterns = await self._load_json(self.patterns_file)
        return [p for p in patterns if p["intent_id"] == intent_id]
    
    async def _load_json(self, file_path: Path) -> List[Dict]:
        """加载JSON文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            return []
    
    async def _save_json(self, file_path: Path, data: List[Dict]):
        """保存JSON文件"""
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)


# 简化的意图识别器
class SimpleIntentRecognizer:
    def __init__(self, storage: SimpleJsonStorage):
        self.storage = storage
    
    async def recognize_intent(self, query: str) -> List[Dict]:
        """识别意图"""
        results = []
        intents = await self.storage.load_intents()
        
        for intent_data in intents:
            if not intent_data.get("is_active", True):
                continue
            
            patterns = await self.storage.load_patterns(intent_data["intent_id"])
            confidence = await self._calculate_confidence(query, patterns)
            
            if confidence > 0:
                results.append({
                    "intent_id": intent_data["intent_id"],
                    "intent_name": intent_data["name"],
                    "display_name": intent_data["display_name"],
                    "confidence": confidence,
                    "reasoning": f"基于模式匹配，置信度: {confidence:.3f}"
                })
        
        # 按置信度排序
        results.sort(key=lambda x: x["confidence"], reverse=True)
        return results
    
    async def _calculate_confidence(self, query: str, patterns: List[Dict]) -> float:
        """计算置信度"""
        if not patterns:
            return 0.0
        
        total_weight = 0.0
        matched_weight = 0.0
        query_lower = query.lower()
        
        for pattern in patterns:
            if not pattern.get("is_active", True):
                continue
            
            weight = pattern.get("weight", 1.0)
            total_weight += weight
            
            pattern_type = pattern["pattern_type"]
            pattern_value = pattern["pattern_value"]
            
            if pattern_type == "keyword":
                if pattern_value.lower() in query_lower:
                    matched_weight += weight
            elif pattern_type == "regex":
                try:
                    if re.search(pattern_value, query, re.IGNORECASE):
                        matched_weight += weight
                except:
                    pass
            elif pattern_type == "semantic":
                # 简化的语义匹配
                keywords = pattern_value.split()
                matched_keywords = sum(1 for kw in keywords if kw.lower() in query_lower)
                if matched_keywords > 0:
                    matched_weight += weight * (matched_keywords / len(keywords))
        
        return matched_weight / total_weight if total_weight > 0 else 0.0


async def main():
    """主测试函数"""
    print("🚀 自定义意图类型简化测试")
    print("=" * 50)
    
    # 1. 初始化存储
    print("\n📋 初始化存储...")
    storage = SimpleJsonStorage()
    await storage.initialize()
    
    # 2. 创建意图识别器
    recognizer = SimpleIntentRecognizer(storage)
    
    # 3. 创建测试意图
    print("\n📝 创建测试意图...")
    
    # 产品咨询意图
    product_intent = CustomIntentType(
        intent_id=str(uuid.uuid4()),
        name="product_inquiry",
        display_name="产品咨询",
        description="用户询问产品相关信息",
        category="business",
        priority=100
    )
    await storage.save_intent(product_intent)
    
    # 添加模式
    patterns = [
        IntentPattern(str(uuid.uuid4()), product_intent.intent_id, "keyword", "产品", 1.0),
        IntentPattern(str(uuid.uuid4()), product_intent.intent_id, "keyword", "价格", 1.2),
        IntentPattern(str(uuid.uuid4()), product_intent.intent_id, "regex", r"(多少钱|费用|收费)", 1.5)
    ]
    
    for pattern in patterns:
        await storage.save_pattern(pattern)
    
    # 添加示例
    examples = [
        IntentExample(str(uuid.uuid4()), product_intent.intent_id, "你们的产品怎么样？", 0.8, True),
        IntentExample(str(uuid.uuid4()), product_intent.intent_id, "产品价格是多少？", 0.9, True),
        IntentExample(str(uuid.uuid4()), product_intent.intent_id, "今天天气怎么样？", 0.1, False)
    ]
    
    for example in examples:
        await storage.save_example(example)
    
    # 技术支持意图
    support_intent = CustomIntentType(
        intent_id=str(uuid.uuid4()),
        name="technical_support",
        display_name="技术支持",
        description="用户寻求技术帮助",
        category="support",
        priority=90
    )
    await storage.save_intent(support_intent)
    
    # 添加技术支持模式
    support_patterns = [
        IntentPattern(str(uuid.uuid4()), support_intent.intent_id, "keyword", "技术支持", 2.0),
        IntentPattern(str(uuid.uuid4()), support_intent.intent_id, "keyword", "帮助", 1.0),
        IntentPattern(str(uuid.uuid4()), support_intent.intent_id, "regex", r"(问题|故障|错误|bug)", 1.3)
    ]
    
    for pattern in support_patterns:
        await storage.save_pattern(pattern)
    
    # 4. 测试意图识别
    print("\n🧪 测试意图识别...")
    
    test_queries = [
        "你们的产品价格是多少？",
        "我需要技术支持",
        "系统出现了bug",
        "今天天气怎么样？",
        "产品功能介绍"
    ]
    
    for query in test_queries:
        print(f"\n🔍 测试查询: '{query}'")
        results = await recognizer.recognize_intent(query)
        
        if results:
            best_result = results[0]
            print(f"  ✅ 最佳匹配: {best_result['display_name']}")
            print(f"  📊 置信度: {best_result['confidence']:.3f}")
            print(f"  💭 推理: {best_result['reasoning']}")
            
            if len(results) > 1:
                print("  📋 其他匹配:")
                for result in results[1:]:
                    print(f"    - {result['display_name']}: {result['confidence']:.3f}")
        else:
            print("  ❌ 未识别到匹配的意图")
    
    # 5. 显示存储的数据
    print("\n📊 存储的数据统计:")
    intents = await storage.load_intents()
    print(f"  - 意图数量: {len(intents)}")
    
    for intent in intents:
        patterns = await storage.load_patterns(intent["intent_id"])
        print(f"  - {intent['display_name']}: {len(patterns)} 个模式")
    
    print("\n🎉 测试完成!")
    print(f"📁 数据存储位置: {storage.storage_dir}")


if __name__ == "__main__":
    asyncio.run(main())
